// ==================================================
// 提携企業ロゴセクション
// ==================================================
.owned-media-partner-logos {
  @include owned-media-section($owned-media-light-gray);

  width: 100%;
  padding: $owned-media-section-padding-desktop 0;
  overflow: hidden;

  // コンテナ
  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: unset;
    padding: 0;
  }

  // PC版ロゴグループ
  &__logos-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    min-height: $owned-media-partner-logos-container-min-height;
  }

  // 各行のロゴ
  &__row {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    gap: $owned-media-partner-logos-row-gap;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;
    overflow: hidden;
    background-color: $owned-media-white;

    &--1 {
      margin-bottom: $owned-media-spacing-small;
    }

    &--2 {
      margin-top: $owned-media-spacing-small;

    }
  }

  &__logo-group {
    @include partner-logo-pc(
      $owned-media-partner-logos-logo-max-height,
      $owned-media-partner-logos-logo-margin
    );
  }

  &__scrolling-container {
    display: none;
  }

  &__scrolling-track {
    display: flex;
    gap: $owned-media-spacing-small;
    align-items: center;
    width: max-content;
  }

  // PC版スクロールトラック
  &__pc-scrolling-track {
    display: flex;
    gap: $owned-media-partner-logos-row-gap;
    align-items: center;
    width: max-content;

    // GPUアクセラレーションを明示的に有効化
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;

    // JavaScriptで制御するため、CSSアニメーションは無効
    // @include scrolling-track-animation-pc($owned-media-animation-duration-normal);
  }

  @include media-breakpoint-down(md) {
    height: $owned-media-partner-logos-mobile-height;
    padding: $owned-media-section-padding-mobile 0;
    overflow: hidden;

    &__container {
      position: relative;
      width: 100%;
      max-width: none;
      height: $owned-media-partner-logos-mobile-container-height;
      padding: 0;
      overflow: hidden;
    }

    &__logos-wrapper {
      display: none;
    }

    &__scrolling-container {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      width: 100%;
      height: $owned-media-partner-logos-mobile-container-height;
    }

    &__scrolling-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: $owned-media-partner-logos-mobile-row-height;
      overflow: hidden;
      background-color: $owned-media-white;

      &--top {
        margin-bottom: $owned-media-spacing-small;

        .owned-media-partner-logos__scrolling-track {
          // GPUアクセラレーションを明示的に有効化
          transform: translateZ(0);
          will-change: transform;
          backface-visibility: hidden;

          // JavaScriptで制御するため、CSSアニメーションは無効
          // @include scrolling-track-animation($owned-media-animation-duration-normal);
        }
      }

      &--bottom {
        margin-top: 0;
        margin-bottom: $owned-media-spacing-large;

        .owned-media-partner-logos__scrolling-track {
          // GPUアクセラレーションを明示的に有効化
          transform: translateZ(0);
          will-change: transform;
          backface-visibility: hidden;

          // JavaScriptで制御するため、CSSアニメーションは無効
          // @include scrolling-track-animation($owned-media-animation-duration-normal);
        }
      }
    }

    &__scrolling-track {
      display: flex;
      gap: $owned-media-partner-logos-mobile-track-gap;
      align-items: center;
      width: max-content;
    }

    &__scrolling-logo {
      @include partner-logo-mobile(
        $owned-media-partner-logos-mobile-logo-max-height,
        $owned-media-partner-logos-mobile-logo-margin
      );
    }
  }

  @keyframes scroll-logos {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(calc(-1 * var(--track-width)));
    }
  }

  @keyframes scroll-logos-pc {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(calc(-1 * var(--track-width)));
    }
  }
}
