/**
 * オウンドメディアサービスページ用JavaScript
 */

(function () {
  'use strict';

  // DOM読み込み完了後に実行
  document.addEventListener('DOMContentLoaded', function () {
    // プログレスマーク初期化
    initProgressMarks();

    // スムーススクロール初期化
    initSmoothScroll();

    // FAQアコーディオン初期化（将来実装用）
    initFAQAccordion();

    // 動的フォーム配置初期化
    initDynamicFormPlacement();
  });

  // 画像を含むすべてのリソースが読み込まれた後に実行
  window.addEventListener('load', function () {
    console.log('[Partner Logos] Window load event fired');
    initPartnerLogosScroll();
  });

  /**
   * プログレスマーク初期化
   */
  function initProgressMarks() {
    const serviceItems = document.querySelectorAll(
      '.owned-media-service__item-number[data-progress]',
    );

    serviceItems.forEach(function (item) {
      const progressValue = parseInt(item.getAttribute('data-progress'));
      updateProgressMark(item, progressValue);
    });
  }

  /**
   * プログレスマークを更新
   * @param {Element} element - 番号要素
   * @param {number} activeStep - アクティブなステップ（1-7）
   */
  function updateProgressMark(element, activeStep) {
    // 7個のドットの背景画像を生成
    const dots = [];
    const backgroundLine = 'linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%)';

    for (let i = 1; i <= 7; i++) {
      if (i === activeStep) {
        // アクティブなドット：白い塗りつぶし + 緑の外周線（2px）
        dots.push(
          'radial-gradient(circle, #ffffff 4px, #ffffff 4px, #7dc8b6 4px, #7dc8b6 6px, transparent 6px)',
        );
      } else {
        // 非アクティブなドット：グレーの塗りつぶし
        dots.push('radial-gradient(circle, #d9d9d9 6px, transparent 6px)');
      }
    }

    // ドットを前面に、背景ラインを後面に配置
    const backgroundImages = [...dots, backgroundLine];

    // 背景位置を設定（ドット7個 + 背景ライン1個）
    const backgroundPositions = [
      '0 0', // ドット1
      '15px 0', // ドット2
      '30px 0', // ドット3
      '44px 0', // ドット4
      '59px 0', // ドット5
      '73px 0', // ドット6
      '88px 0', // ドット7
      '11px 11px', // 背景ライン
    ];

    // 背景サイズを設定
    const backgroundSizes = [
      '24px 24px', // ドット1
      '24px 24px', // ドット2
      '24px 24px', // ドット3
      '24px 24px', // ドット4
      '24px 24px', // ドット5
      '24px 24px', // ドット6
      '24px 24px', // ドット7
      '88px 3px', // 背景ライン
    ];

    // 擬似要素のスタイルを動的に設定
    const style = document.createElement('style');
    const uniqueClass = 'progress-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    element.classList.add(uniqueClass);

    style.textContent = `
      .${uniqueClass}::before {
        background-image: ${backgroundImages.join(', ')};
        background-position: ${backgroundPositions.join(', ')};
        background-size: ${backgroundSizes.join(', ')};
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 提携企業ロゴの無限スクロール初期化
   */
  function initPartnerLogosScroll() {
    const logoSection = document.querySelector('.owned-media-partner-logos');

    // スマホ版のスクロールトラック（2行とも統合されたロゴセットを使用）
    const scrollingTracks = document.querySelectorAll(
      '.owned-media-partner-logos__scrolling-track',
    );

    // PC版のロゴ行
    const pcLogoRows = document.querySelectorAll('.owned-media-partner-logos__row');

    if (!logoSection) return;

    // スマホ版の無限スクロール設定（2行とも統合されたロゴセットを使用）
    if (scrollingTracks.length > 0) {
      scrollingTracks.forEach(function (track) {
        console.log(
          '[Mobile Logo] Found track with logos:',
          track.children.length,
          'data-row:',
          track.getAttribute('data-row'),
        );
        initInfiniteScroll(track, false);
      });
    }

    // PC版の無限スクロール設定
    if (pcLogoRows.length > 0) {
      console.log('[PC Logo] Found rows:', pcLogoRows.length);

      pcLogoRows.forEach(function (row, index) {
        // スクロール用のトラックを作成
        const scrollTrack = document.createElement('div');
        scrollTrack.className = 'owned-media-partner-logos__pc-scrolling-track';
        scrollTrack.setAttribute('data-row', index === 0 ? 'top' : 'bottom');

        // 元のロゴをトラックに移動
        const originalLogos = Array.from(row.children);

        originalLogos.forEach(function (logo) {
          scrollTrack.appendChild(logo);
        });

        // トラックを行に追加
        row.innerHTML = '';
        row.appendChild(scrollTrack);

        // 無限スクロールを初期化
        initInfiniteScroll(scrollTrack, true);
      });
    }
  }

  /**
   * 無限スクロールの初期化
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {boolean} isPC - PC版かどうか
   */
  function initInfiniteScroll(track, isPC) {
    const originalLogos = Array.from(track.children);

    if (originalLogos.length === 0) return;

    // aria-hidden属性を追加（スクリーンリーダー対策）
    track.setAttribute('aria-hidden', 'true');

    // 各ロゴの実際の幅とマージンを計算
    let logoSetWidth = 0;
    originalLogos.forEach((logo) => {
      const style = window.getComputedStyle(logo);
      const width = logo.offsetWidth;
      const marginLeft = parseInt(style.marginLeft, 10) || 0;
      const marginRight = parseInt(style.marginRight, 10) || 0;
      logoSetWidth += width + marginLeft + marginRight;
    });

    // ギャップも考慮（PC版とモバイル版で異なる）
    const gapSize = isPC ? 25 : 30; // SCSSの$owned-media-partner-logos-row-gapと$owned-media-partner-logos-mobile-track-gap
    logoSetWidth += gapSize * (originalLogos.length - 1);

    // 計算した実際のセット幅を使用
    const singleSetWidth = logoSetWidth;

    console.log(
      `[${isPC ? 'PC' : 'Mobile'} Logo] 無限スクロール: 実際のロゴセット幅: ${singleSetWidth}px`,
    );

    // スクロール速度（ピクセル/秒）
    const scrollSpeed = isPC ? 30 : 40; // PC版は少し遅く

    // 無限スクロールアニメーションを開始
    startInfiniteScrollAnimation(track, originalLogos, singleSetWidth, scrollSpeed);
  }

  /**
   * ロゴセットを作成
   * @param {Array} originalLogos - 元のロゴ配列
   * @returns {Array} 複製されたロゴ配列
   */
  function createLogoSet(originalLogos) {
    return originalLogos.map(function (logo) {
      const clonedLogo = logo.cloneNode(true);
      clonedLogo.setAttribute('aria-hidden', 'true');
      return clonedLogo;
    });
  }

  /**
   * 無限スクロールアニメーション（動的追加・削除版）
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {Array} originalLogos - 元のロゴ配列
   * @param {number} singleSetWidth - 1セットの幅
   * @param {number} scrollSpeed - スクロール速度
   */
  function startInfiniteScrollAnimation(track, originalLogos, singleSetWidth, scrollSpeed) {
    let currentPosition = 0;
    let lastTimestamp = null;
    let logoSets = []; // 現在表示中のロゴセット管理
    let nextSetId = 0; // 次のセットID

    // will-changeを設定してGPUアクセラレーションを有効化
    track.style.willChange = 'transform';

    // 初期セットを追加（最初の1セットは既に存在）
    logoSets.push({
      id: nextSetId++,
      elements: Array.from(track.children),
      startPosition: 0,
    });

    // 2セット目を追加
    addNextLogoSet();
    // 3セット目を追加
    addNextLogoSet();

    console.log(`[Logo] 無限スクロール開始:`);
    console.log(`  - 1セット幅: ${singleSetWidth}px`);
    console.log(`  - ロゴ数: ${originalLogos.length}`);
    console.log(`  - スクロール速度: ${scrollSpeed}px/秒`);

    /**
     * 次のロゴセットを追加
     */
    function addNextLogoSet() {
      const newSet = createLogoSet(originalLogos);
      const setStartPosition = logoSets.length * singleSetWidth;

      // DocumentFragmentを使用して一括追加
      const fragment = document.createDocumentFragment();
      newSet.forEach((logo) => fragment.appendChild(logo));
      track.appendChild(fragment);

      logoSets.push({
        id: nextSetId++,
        elements: newSet,
        startPosition: setStartPosition,
      });

      console.log(`[Logo] セット追加: ID=${nextSetId - 1}, 位置=${setStartPosition}px`);
    }

    /**
     * 画面外に出たロゴセットを削除
     */
    function removeOffscreenSets() {
      const viewportWidth = window.innerWidth;
      const removeThreshold = -singleSetWidth - viewportWidth; // 画面外に完全に出た位置

      logoSets = logoSets.filter((set) => {
        const setCurrentPosition = set.startPosition - currentPosition;

        if (setCurrentPosition < removeThreshold) {
          // セットを削除
          set.elements.forEach((element) => {
            if (element.parentNode === track) {
              track.removeChild(element);
            }
          });
          console.log(`[Logo] セット削除: ID=${set.id}, 位置=${setCurrentPosition.toFixed(2)}px`);
          return false;
        }
        return true;
      });

      // セットの位置を再計算（削除後の調整）
      logoSets.forEach((set, index) => {
        set.startPosition = index * singleSetWidth;
      });
    }

    /**
     * 必要に応じて新しいセットを追加
     */
    function ensureSufficientSets() {
      const viewportWidth = window.innerWidth;
      const lastSet = logoSets[logoSets.length - 1];
      const lastSetPosition = lastSet.startPosition - currentPosition;
      const addThreshold = viewportWidth * 2; // 画面幅の2倍先まで準備

      if (lastSetPosition < addThreshold) {
        addNextLogoSet();
      }
    }

    function animate(timestamp) {
      if (!lastTimestamp) {
        lastTimestamp = timestamp;
      }

      // 実際の経過時間を使用してフレームレート非依存にする
      const deltaTime = timestamp - lastTimestamp;
      const deltaPosition = (scrollSpeed * deltaTime) / 1000; // ミリ秒を秒に変換

      currentPosition += deltaPosition;

      // 定期的にセット管理を実行（パフォーマンス考慮で60fpsではなく30fps程度で実行）
      if (Math.floor(timestamp / 33) !== Math.floor(lastTimestamp / 33)) {
        // 画面外のセットを削除
        removeOffscreenSets();
        // 必要に応じて新しいセットを追加
        ensureSufficientSets();
      }

      // translate3dを使用してハードウェアアクセラレーションを活用
      track.style.transform = `translate3d(-${currentPosition}px, 0, 0)`;

      lastTimestamp = timestamp;
      requestAnimationFrame(animate);
    }

    requestAnimationFrame(animate);
  }

  /**
   * スムーススクロール初期化
   */
  function initSmoothScroll() {
    const ctaButtons = document.querySelectorAll(
      '.owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper',
    );

    ctaButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        // 実際のフォームが実装されるまでは、お問い合わせセクションへスクロール
        const contactSection = document.querySelector('.section_contact');
        if (contactSection) {
          e.preventDefault();
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      });
    });
  }

  /**
   * FAQアコーディオン初期化（将来実装用）
   */
  function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.owned-media__faq-item');

    faqItems.forEach(function (item) {
      const question = item.querySelector('.owned-media__faq-question');
      const answer = item.querySelector('.owned-media__faq-answer');

      if (question && answer) {
        question.addEventListener('click', function () {
          const isOpen = item.classList.contains('is-open');

          // 他のFAQを閉じる
          faqItems.forEach(function (otherItem) {
            otherItem.classList.remove('is-open');
            const otherAnswer = otherItem.querySelector('.owned-media__faq-answer');
            if (otherAnswer) {
              otherAnswer.style.maxHeight = '0';
            }
          });

          // クリックされたFAQの開閉
          if (!isOpen) {
            item.classList.add('is-open');
            answer.style.maxHeight = answer.scrollHeight + 'px';
          }
        });
      }
    });
  }

  /**
   * パフォーマンス最適化：debounce関数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = function () {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * モバイルデバイス検出
   */
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }

  /**
   * 動的フォーム配置初期化
   */
  function initDynamicFormPlacement() {
    // フォームが配置された後に実行
    setTimeout(function () {
      const originalFormContainer = document.querySelector('.section__contact__cont');
      const topFormContainer = document.getElementById('top-form-container');

      if (!originalFormContainer || !topFormContainer) return;

      // フォーム要素を探す（BowNowフォームはscriptタグまたはiframeとして挿入される）
      let formElement = null;

      // scriptタグ、iframe、その他のフォーム要素を探す
      const possibleFormElements = originalFormContainer.querySelectorAll(
        'script, iframe, form, div[id*="bownow"], div[class*="bownow"]',
      );

      if (possibleFormElements.length > 0) {
        // フォーム関連要素をすべて取得
        formElement = document.createElement('div');
        formElement.className = 'bownow-form-wrapper';

        // 元のコンテナの全内容を保存
        formElement.innerHTML = originalFormContainer.innerHTML;
      }

      if (!formElement) return;

      // フォームの現在位置を追跡
      let formIsInTop = false;
      // フォーム移動を一時停止するフラグ
      let formMovementPaused = false;

      // フォームを移動する関数（改善版）
      function moveFormTo(targetContainer, sourceContainer) {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log('[Form Movement] 移動が一時停止中のため、フォーム移動をスキップします');
          return;
        }

        // コンテナの存在確認
        if (!targetContainer || !sourceContainer) {
          console.log('[Form Movement] コンテナが見つからないため、フォーム移動をスキップします');
          return;
        }

        // フォーム送信中の場合は移動を避ける（追加の安全措置）
        const isFormSubmitting =
          sourceContainer.querySelector('.show-page-btn .btn[disabled]') !== null ||
          targetContainer.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Form Movement] フォーム送信中のため、フォーム移動をスキップします');
          return;
        }

        // DOM要素を安全に移動（イベントリスナーを保持）
        try {
          while (sourceContainer.firstChild) {
            targetContainer.appendChild(sourceContainer.firstChild);
          }
        } catch (error) {
          console.error('[Form Movement] フォーム移動中にエラーが発生しました:', error);
        }
      }

      // フォーム送信イベントの監視（軽量化版）
      function setupFormSubmissionHandling() {
        // フォーム送信時の処理（最小限の干渉）
        function handleFormSubmission() {
          console.log('[Form Submission] フォーム送信を検出しました');

          // フォーム移動を一時停止（送信処理を阻害しないよう最小限の処理）
          formMovementPaused = true;
          console.log('[Form Movement] フォーム移動を一時停止しました');

          // 送信処理を阻害しないため、スクロール防止処理は削除
          // 外部フォーム（BowNow）の正常な動作を優先

          // 短時間後にフォーム移動を再開（送信処理完了を想定）
          setTimeout(function () {
            formMovementPaused = false;
            console.log('[Form Movement] フォーム移動を再開しました');

            // 現在のスクロール位置に基づいてフォーム位置を調整
            handleScroll();
          }, 2000); // 2秒後に再開（処理時間を短縮）
        }

        // 軽量な監視設定（重複を避けるため一度だけ設定）
        const monitorFormSubmission = function () {
          // 送信ボタンのクリックイベントのみ監視（最小限の干渉）
          const submitButtons = document.querySelectorAll(
            'input[type="submit"], button[type="submit"], .btn[class*="submit"], [id*="submit"]',
          );

          submitButtons.forEach(function (button) {
            // 重複登録を防ぐためのフラグをチェック
            if (!button.hasAttribute('data-form-monitor-attached')) {
              button.setAttribute('data-form-monitor-attached', 'true');
              button.addEventListener('click', handleFormSubmission, {
                once: false,
                passive: true,
              });
            }
          });
        };

        // 初期監視設定
        monitorFormSubmission();

        // DOM変更を監視（軽量化）
        const observer = new MutationObserver(function (mutations) {
          let shouldReMonitor = false;

          mutations.forEach(function (mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              mutation.addedNodes.forEach(function (node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  // 新しく追加された送信ボタンのみ監視
                  const hasSubmitElements =
                    node.querySelectorAll &&
                    node.querySelectorAll('input[type="submit"], button[type="submit"], .btn')
                      .length > 0;

                  if (hasSubmitElements) {
                    shouldReMonitor = true;
                  }
                }
              });
            }
          });

          // 必要な場合のみ再監視
          if (shouldReMonitor) {
            setTimeout(monitorFormSubmission, 100);
          }
        });

        // DOM全体を監視（軽量化）
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      }

      // 初期配置（ページ上部に配置）
      moveFormTo(topFormContainer, originalFormContainer);
      formIsInTop = true;

      // スクロールイベントハンドラー（改善版）
      const handleScroll = debounce(function () {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log(
            '[Scroll Handler] フォーム移動が一時停止中のため、スクロール処理をスキップします',
          );
          return;
        }

        // フォーム送信中かどうかをチェック（追加の安全措置）
        const isFormSubmitting = document.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Scroll Handler] フォーム送信中のため、スクロール処理をスキップします');
          return;
        }

        const scrollPosition = window.scrollY || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight;
        const halfwayPoint = documentHeight / 2;

        // スクロール位置が画面の半分を超えたら
        if (scrollPosition > halfwayPoint && formIsInTop) {
          // フォームを元の位置（下部）に戻す
          moveFormTo(originalFormContainer, topFormContainer);
          formIsInTop = false;
          console.log('[Form Movement] フォームを下部に移動しました');
        } else if (scrollPosition <= halfwayPoint && !formIsInTop) {
          // フォームを上部に移動
          moveFormTo(topFormContainer, originalFormContainer);
          formIsInTop = true;
          console.log('[Form Movement] フォームを上部に移動しました');
        }
      }, 150);

      // スクロールイベントをリッスン
      window.addEventListener('scroll', handleScroll);

      // フォーム送信処理の設定
      setupFormSubmissionHandling();

      // 初期スクロール位置をチェック
      handleScroll();
    }, 1000); // BowNowフォームの読み込みを待つ
  }

  /**
   * タッチデバイス用の最適化
   */
  function initTouchOptimizations() {
    if (isMobileDevice()) {
      // タッチデバイス用のクラスを追加
      document.documentElement.classList.add('touch-device');

      // iOS Safariでの100vh問題を解決
      const setVH = function () {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', vh + 'px');
      };

      setVH();
      window.addEventListener('resize', debounce(setVH, 250));
    }
  }

  // タッチ最適化を初期化
  initTouchOptimizations();
})();
