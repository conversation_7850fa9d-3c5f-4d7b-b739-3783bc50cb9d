/**
 * オウンドメディアサービスページ用JavaScript
 */

(function () {
  'use strict';

  // DOM読み込み完了後に実行
  document.addEventListener('DOMContentLoaded', function () {
    // プログレスマーク初期化
    initProgressMarks();

    // スムーススクロール初期化
    initSmoothScroll();

    // FAQアコーディオン初期化（将来実装用）
    initFAQAccordion();

    // 動的フォーム配置初期化
    initDynamicFormPlacement();

    // 高度な遅延読み込み初期化
    initAdvancedLazyLoading();

    // 画面サイズ変更時の再初期化（デバウンス付き）
    let resizeTimeout;
    window.addEventListener('resize', function () {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(function () {
        // 既存のアニメーションを停止
        const existingTracks = document.querySelectorAll(
          '.owned-media-partner-logos__pc-scrolling-track, .owned-media-partner-logos__scrolling-track',
        );
        existingTracks.forEach(function (track) {
          track.style.transform = '';
          track.style.willChange = '';
        });

        // パートナーロゴの無限スクロールを再初期化
        initPartnerLogosScroll();
      }, 250);
    });
  });

  // 画像を含むすべてのリソースが読み込まれた後に実行
  window.addEventListener('load', function () {
    console.log('[Partner Logos] Window load event fired');
    initPartnerLogosScroll();
  });

  /**
   * プログレスマーク初期化
   */
  function initProgressMarks() {
    const serviceItems = document.querySelectorAll(
      '.owned-media-service__item-number[data-progress]',
    );

    serviceItems.forEach(function (item) {
      const progressValue = parseInt(item.getAttribute('data-progress'));
      updateProgressMark(item, progressValue);
    });
  }

  /**
   * プログレスマークを更新
   * @param {Element} element - 番号要素
   * @param {number} activeStep - アクティブなステップ（1-7）
   */
  function updateProgressMark(element, activeStep) {
    // 7個のドットの背景画像を生成
    const dots = [];
    const backgroundLine = 'linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%)';

    for (let i = 1; i <= 7; i++) {
      if (i === activeStep) {
        // アクティブなドット：白い塗りつぶし + 緑の外周線（2px）
        dots.push(
          'radial-gradient(circle, #ffffff 4px, #ffffff 4px, #7dc8b6 4px, #7dc8b6 6px, transparent 6px)',
        );
      } else {
        // 非アクティブなドット：グレーの塗りつぶし
        dots.push('radial-gradient(circle, #d9d9d9 6px, transparent 6px)');
      }
    }

    // ドットを前面に、背景ラインを後面に配置
    const backgroundImages = [...dots, backgroundLine];

    // 背景位置を設定（ドット7個 + 背景ライン1個）
    const backgroundPositions = [
      '0 0', // ドット1
      '15px 0', // ドット2
      '30px 0', // ドット3
      '44px 0', // ドット4
      '59px 0', // ドット5
      '73px 0', // ドット6
      '88px 0', // ドット7
      '11px 11px', // 背景ライン
    ];

    // 背景サイズを設定
    const backgroundSizes = [
      '24px 24px', // ドット1
      '24px 24px', // ドット2
      '24px 24px', // ドット3
      '24px 24px', // ドット4
      '24px 24px', // ドット5
      '24px 24px', // ドット6
      '24px 24px', // ドット7
      '88px 3px', // 背景ライン
    ];

    // 擬似要素のスタイルを動的に設定
    const style = document.createElement('style');
    const uniqueClass =
      'progress-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
    element.classList.add(uniqueClass);

    style.textContent = `
      .${uniqueClass}::before {
        background-image: ${backgroundImages.join(', ')};
        background-position: ${backgroundPositions.join(', ')};
        background-size: ${backgroundSizes.join(', ')};
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 提携企業ロゴの無限スクロール初期化
   */
  /**
   * 現在の表示状態（PC/モバイル）を判定
   */
  function getCurrentDisplayMode() {
    // モバイル版のコンテナが表示されているかチェック
    const mobileContainer = document.querySelector(
      '.owned-media-partner-logos__scrolling-container',
    );
    if (mobileContainer) {
      const mobileStyle = window.getComputedStyle(mobileContainer);
      if (mobileStyle.display !== 'none') {
        return 'mobile';
      }
    }

    // PC版のロゴラッパーが表示されているかチェック
    const pcWrapper = document.querySelector('.owned-media-partner-logos__logos-wrapper');
    if (pcWrapper) {
      const pcStyle = window.getComputedStyle(pcWrapper);
      if (pcStyle.display !== 'none') {
        return 'pc';
      }
    }

    // フォールバック：画面幅で判定
    return window.innerWidth >= 768 ? 'pc' : 'mobile';
  }

  function initPartnerLogosScroll() {
    const logoSection = document.querySelector('.owned-media-partner-logos');
    if (!logoSection) return;

    const currentMode = getCurrentDisplayMode();
    console.log(`[Partner Logos] Current display mode: ${currentMode}`);

    if (currentMode === 'mobile') {
      // モバイル版の初期化
      const scrollingTracks = document.querySelectorAll(
        '.owned-media-partner-logos__scrolling-track',
      );
      if (scrollingTracks.length > 0) {
        scrollingTracks.forEach(function (track) {
          console.log(
            '[Mobile Logo] Found track with logos:',
            track.children.length,
            'data-row:',
            track.getAttribute('data-row'),
          );

          // モバイル版は少し遅延させてDOM要素が完全に表示されてから初期化
          setTimeout(function () {
            initInfiniteScroll(track, false);
          }, 100);
        });
      }
    } else {
      // PC版の初期化
      const pcLogoRows = document.querySelectorAll('.owned-media-partner-logos__row');
      if (pcLogoRows.length > 0) {
        console.log('[PC Logo] Found rows:', pcLogoRows.length);

        pcLogoRows.forEach(function (row, index) {
          // スクロール用のトラックを作成
          const scrollTrack = document.createElement('div');
          scrollTrack.className = 'owned-media-partner-logos__pc-scrolling-track';
          scrollTrack.setAttribute('data-row', index === 0 ? 'top' : 'bottom');

          // 元のロゴをトラックに移動
          const originalLogos = Array.from(row.children);

          originalLogos.forEach(function (logo) {
            scrollTrack.appendChild(logo);
          });

          // トラックを行に追加
          row.innerHTML = '';
          row.appendChild(scrollTrack);

          // 無限スクロールを初期化（PC版も遅延実行）
          setTimeout(function () {
            initInfiniteScroll(scrollTrack, true);
          }, 50);
        });
      }
    }
  }

  /**
   * 無限スクロールの初期化（最適化版）
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {boolean} isPC - PC版かどうか
   */
  function initInfiniteScroll(track, isPC) {
    const originalLogos = Array.from(track.children);

    if (originalLogos.length === 0) return;

    // モバイル版の場合、親要素の表示状態を確認
    if (!isPC) {
      const parentContainer = track.closest('.owned-media-partner-logos__scrolling-container');
      if (parentContainer) {
        const containerStyle = window.getComputedStyle(parentContainer);
        if (containerStyle.display === 'none' || containerStyle.visibility === 'hidden') {
          console.warn('[Mobile Logo] 親コンテナが非表示のため、初期化をスキップします');
          return;
        }
      }
    }

    // aria-hidden属性を追加（スクリーンリーダー対策）
    track.setAttribute('aria-hidden', 'true');

    // パフォーマンス最適化：幅計算を一度だけ実行し、結果をキャッシュ
    const logoSetWidth = calculateLogoSetWidth(originalLogos, isPC);

    console.log(
      `[${isPC ? 'PC' : 'Mobile'} Logo] 無限スクロール: 実際のロゴセット幅: ${logoSetWidth}px`,
    );

    // スクロール速度（ピクセル/秒）
    const scrollSpeed = isPC ? 30 : 40; // PC版は少し遅く

    // 無限スクロールアニメーションを開始（最適化版）
    startOptimizedInfiniteScrollAnimation(track, originalLogos, logoSetWidth, scrollSpeed, isPC);
  }

  /**
   * ロゴセット幅を効率的に計算（キャッシュ機能付き）
   * @param {Array} originalLogos - 元のロゴ配列
   * @param {boolean} isPC - PC版かどうか
   * @returns {number} ロゴセットの幅
   */
  function calculateLogoSetWidth(originalLogos, isPC) {
    // キャッシュキーを生成
    const cacheKey = `logoSetWidth_${isPC ? 'pc' : 'mobile'}_${originalLogos.length}`;

    // キャッシュから取得を試行
    if (window.logoWidthCache && window.logoWidthCache[cacheKey]) {
      return window.logoWidthCache[cacheKey];
    }

    // 初回計算時のみDOM計算を実行
    let logoSetWidth = 0;

    // 実際のトラックと同じクラス名を使用して測定用コンテナを作成
    const measureContainer = document.createElement('div');
    measureContainer.className = isPC
      ? 'owned-media-partner-logos__pc-scrolling-track'
      : 'owned-media-partner-logos__scrolling-track';

    // モバイル版の場合、より確実な測定のため親要素も作成
    let parentContainer = null;
    if (!isPC) {
      parentContainer = document.createElement('div');
      parentContainer.className = 'owned-media-partner-logos__scrolling-row';
      parentContainer.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        visibility: hidden;
        width: 100vw;
        overflow: visible;
      `;
      parentContainer.appendChild(measureContainer);
    } else {
      measureContainer.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        visibility: hidden;
      `;
    }

    // ロゴを測定用コンテナに複製（実際のクラス名を使用）
    originalLogos.forEach((logo) => {
      const clonedLogo = logo.cloneNode(true);
      // 実際のロゴクラス名を保持
      clonedLogo.className = isPC
        ? 'owned-media-partner-logos__logo-group'
        : 'owned-media-partner-logos__scrolling-logo';
      measureContainer.appendChild(clonedLogo);
    });

    document.body.appendChild(parentContainer || measureContainer);
    logoSetWidth = measureContainer.offsetWidth;
    document.body.removeChild(parentContainer || measureContainer);

    console.log(`[${isPC ? 'PC' : 'Mobile'} Logo] 計算されたセット幅: ${logoSetWidth}px`);

    // キャッシュに保存
    if (!window.logoWidthCache) {
      window.logoWidthCache = {};
    }
    window.logoWidthCache[cacheKey] = logoSetWidth;

    return logoSetWidth;
  }

  /**
   * 最適化された無限スクロールアニメーション
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {Array} originalLogos - 元のロゴ配列
   * @param {number} singleSetWidth - 1セットの幅
   * @param {number} scrollSpeed - スクロール速度
   * @param {boolean} isPC - PC版かどうか
   */
  function startOptimizedInfiniteScrollAnimation(
    track,
    originalLogos,
    singleSetWidth,
    scrollSpeed,
    isPC,
  ) {
    // 初期位置をランダムに設定して、複数のトラックが同期しないようにする
    let currentPosition = Math.random() * singleSetWidth * 0.5; // 0〜50%の範囲でランダム開始
    let lastTimestamp = null;

    // パフォーマンス最適化：セット管理を簡素化
    const totalSets = 3; // 必要最小限のセット数
    let isInitialized = false;

    // GPUアクセラレーションを有効化
    track.style.willChange = 'transform';
    track.style.transform = 'translateZ(0)'; // ハードウェアアクセラレーション強制

    console.log(`[${isPC ? 'PC' : 'Mobile'} Logo] 最適化無限スクロール開始:`);
    console.log(`  - 1セット幅: ${singleSetWidth}px`);
    console.log(`  - ロゴ数: ${originalLogos.length}`);
    console.log(`  - スクロール速度: ${scrollSpeed}px/秒`);
    console.log(`  - トラック実際の幅: ${track.offsetWidth}px`);
    console.log(`  - トラック計算後の幅: ${track.scrollWidth}px`);

    // 初期化：必要なセットを一度だけ作成
    function initializeLogoSets() {
      if (isInitialized) return;

      const fragment = document.createDocumentFragment();

      // 追加セットを作成（元のセット + 2つの複製）
      for (let i = 1; i < totalSets; i++) {
        originalLogos.forEach((logo) => {
          const clonedLogo = logo.cloneNode(true);
          clonedLogo.setAttribute('aria-hidden', 'true');
          fragment.appendChild(clonedLogo);
        });
      }

      track.appendChild(fragment);
      isInitialized = true;
    }

    // 最適化されたアニメーション関数
    function animate(timestamp) {
      if (!lastTimestamp) {
        lastTimestamp = timestamp;
        initializeLogoSets(); // 初回のみセットを初期化

        // 初期化後に実際の幅を再確認（PC版・モバイル版共通）
        const actualWidth = track.offsetWidth;
        if (actualWidth === 0) {
          console.warn(
            `[${isPC ? 'PC' : 'Mobile'} Logo] トラック幅が0です。アニメーションを停止します。`,
          );
          return;
        }
        console.log(`[${isPC ? 'PC' : 'Mobile'} Logo] 初期化後のトラック幅: ${actualWidth}px`);
      }

      // 実際の経過時間を使用してフレームレート非依存にする
      const deltaTime = timestamp - lastTimestamp;
      const deltaPosition = (scrollSpeed * deltaTime) / 1000; // ミリ秒を秒に変換

      currentPosition += deltaPosition;

      // 無限ループ処理：位置がセット幅を超えたらリセット
      // より滑らかなリセットのため、剰余演算を使用
      if (currentPosition >= singleSetWidth) {
        currentPosition = currentPosition % singleSetWidth;
      }

      // translate3dを使用してハードウェアアクセラレーションを活用
      track.style.transform = `translate3d(-${currentPosition}px, 0, 0)`;

      lastTimestamp = timestamp;
      requestAnimationFrame(animate);
    }

    // アニメーション開始
    requestAnimationFrame(animate);

    // ページ非表示時にアニメーションを停止（パフォーマンス最適化）
    document.addEventListener('visibilitychange', function () {
      if (document.hidden) {
        // ページが非表示になったらアニメーションを停止
        track.style.animationPlayState = 'paused';
      } else {
        // ページが表示されたらアニメーションを再開
        track.style.animationPlayState = 'running';
      }
    });
  }

  /**
   * スムーススクロール初期化
   */
  function initSmoothScroll() {
    const ctaButtons = document.querySelectorAll(
      '.owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper',
    );

    ctaButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        // 実際のフォームが実装されるまでは、お問い合わせセクションへスクロール
        const contactSection = document.querySelector('.section_contact');
        if (contactSection) {
          e.preventDefault();
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      });
    });
  }

  /**
   * FAQアコーディオン初期化（将来実装用）
   */
  function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.owned-media__faq-item');

    faqItems.forEach(function (item) {
      const question = item.querySelector('.owned-media__faq-question');
      const answer = item.querySelector('.owned-media__faq-answer');

      if (question && answer) {
        question.addEventListener('click', function () {
          const isOpen = item.classList.contains('is-open');

          // 他のFAQを閉じる
          faqItems.forEach(function (otherItem) {
            otherItem.classList.remove('is-open');
            const otherAnswer = otherItem.querySelector('.owned-media__faq-answer');
            if (otherAnswer) {
              otherAnswer.style.maxHeight = '0';
            }
          });

          // クリックされたFAQの開閉
          if (!isOpen) {
            item.classList.add('is-open');
            answer.style.maxHeight = answer.scrollHeight + 'px';
          }
        });
      }
    });
  }

  /**
   * パフォーマンス最適化：debounce関数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = function () {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * モバイルデバイス検出
   */
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }

  /**
   * 動的フォーム配置初期化
   */
  function initDynamicFormPlacement() {
    // フォームが配置された後に実行
    setTimeout(function () {
      const originalFormContainer = document.querySelector('.section__contact__cont');
      const topFormContainer = document.getElementById('top-form-container');

      if (!originalFormContainer || !topFormContainer) return;

      // フォーム要素を探す（BowNowフォームはscriptタグまたはiframeとして挿入される）
      let formElement = null;

      // scriptタグ、iframe、その他のフォーム要素を探す
      const possibleFormElements = originalFormContainer.querySelectorAll(
        'script, iframe, form, div[id*="bownow"], div[class*="bownow"]',
      );

      if (possibleFormElements.length > 0) {
        // フォーム関連要素をすべて取得
        formElement = document.createElement('div');
        formElement.className = 'bownow-form-wrapper';

        // 元のコンテナの全内容を保存
        formElement.innerHTML = originalFormContainer.innerHTML;
      }

      if (!formElement) return;

      // フォームの現在位置を追跡
      let formIsInTop = false;
      // フォーム移動を一時停止するフラグ
      let formMovementPaused = false;

      // フォームを移動する関数（改善版）
      function moveFormTo(targetContainer, sourceContainer) {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log('[Form Movement] 移動が一時停止中のため、フォーム移動をスキップします');
          return;
        }

        // コンテナの存在確認
        if (!targetContainer || !sourceContainer) {
          console.log('[Form Movement] コンテナが見つからないため、フォーム移動をスキップします');
          return;
        }

        // フォーム送信中の場合は移動を避ける（追加の安全措置）
        const isFormSubmitting =
          sourceContainer.querySelector('.show-page-btn .btn[disabled]') !== null ||
          targetContainer.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Form Movement] フォーム送信中のため、フォーム移動をスキップします');
          return;
        }

        // DOM要素を安全に移動（イベントリスナーを保持）
        try {
          while (sourceContainer.firstChild) {
            targetContainer.appendChild(sourceContainer.firstChild);
          }
        } catch (error) {
          console.error('[Form Movement] フォーム移動中にエラーが発生しました:', error);
        }
      }

      // フォーム送信イベントの監視（軽量化版）
      function setupFormSubmissionHandling() {
        // フォーム送信時の処理（最小限の干渉）
        function handleFormSubmission() {
          console.log('[Form Submission] フォーム送信を検出しました');

          // フォーム移動を一時停止（送信処理を阻害しないよう最小限の処理）
          formMovementPaused = true;
          console.log('[Form Movement] フォーム移動を一時停止しました');

          // 送信処理を阻害しないため、スクロール防止処理は削除
          // 外部フォーム（BowNow）の正常な動作を優先

          // 短時間後にフォーム移動を再開（送信処理完了を想定）
          setTimeout(function () {
            formMovementPaused = false;
            console.log('[Form Movement] フォーム移動を再開しました');

            // 現在のスクロール位置に基づいてフォーム位置を調整
            handleScroll();
          }, 2000); // 2秒後に再開（処理時間を短縮）
        }

        // 軽量な監視設定（重複を避けるため一度だけ設定）
        const monitorFormSubmission = function () {
          // 送信ボタンのクリックイベントのみ監視（最小限の干渉）
          const submitButtons = document.querySelectorAll(
            'input[type="submit"], button[type="submit"], .btn[class*="submit"], [id*="submit"]',
          );

          submitButtons.forEach(function (button) {
            // 重複登録を防ぐためのフラグをチェック
            if (!button.hasAttribute('data-form-monitor-attached')) {
              button.setAttribute('data-form-monitor-attached', 'true');
              button.addEventListener('click', handleFormSubmission, {
                once: false,
                passive: true,
              });
            }
          });
        };

        // 初期監視設定
        monitorFormSubmission();

        // DOM変更を監視（軽量化）
        const observer = new MutationObserver(function (mutations) {
          let shouldReMonitor = false;

          mutations.forEach(function (mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              mutation.addedNodes.forEach(function (node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  // 新しく追加された送信ボタンのみ監視
                  const hasSubmitElements =
                    node.querySelectorAll &&
                    node.querySelectorAll('input[type="submit"], button[type="submit"], .btn')
                      .length > 0;

                  if (hasSubmitElements) {
                    shouldReMonitor = true;
                  }
                }
              });
            }
          });

          // 必要な場合のみ再監視
          if (shouldReMonitor) {
            setTimeout(monitorFormSubmission, 100);
          }
        });

        // DOM全体を監視（軽量化）
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      }

      // 初期配置（ページ上部に配置）
      moveFormTo(topFormContainer, originalFormContainer);
      formIsInTop = true;

      // スクロールイベントハンドラー（改善版）
      const handleScroll = debounce(function () {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log(
            '[Scroll Handler] フォーム移動が一時停止中のため、スクロール処理をスキップします',
          );
          return;
        }

        // フォーム送信中かどうかをチェック（追加の安全措置）
        const isFormSubmitting = document.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Scroll Handler] フォーム送信中のため、スクロール処理をスキップします');
          return;
        }

        const scrollPosition = window.scrollY || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight;
        const halfwayPoint = documentHeight / 2;

        // スクロール位置が画面の半分を超えたら
        if (scrollPosition > halfwayPoint && formIsInTop) {
          // フォームを元の位置（下部）に戻す
          moveFormTo(originalFormContainer, topFormContainer);
          formIsInTop = false;
          console.log('[Form Movement] フォームを下部に移動しました');
        } else if (scrollPosition <= halfwayPoint && !formIsInTop) {
          // フォームを上部に移動
          moveFormTo(topFormContainer, originalFormContainer);
          formIsInTop = true;
          console.log('[Form Movement] フォームを上部に移動しました');
        }
      }, 150);

      // スクロールイベントをリッスン
      window.addEventListener('scroll', handleScroll);

      // フォーム送信処理の設定
      setupFormSubmissionHandling();

      // 初期スクロール位置をチェック
      handleScroll();
    }, 1000); // BowNowフォームの読み込みを待つ
  }

  /**
   * タッチデバイス用の最適化
   */
  function initTouchOptimizations() {
    if (isMobileDevice()) {
      // タッチデバイス用のクラスを追加
      document.documentElement.classList.add('touch-device');

      // iOS Safariでの100vh問題を解決
      const setVH = function () {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', vh + 'px');
      };

      setVH();
      window.addEventListener('resize', debounce(setVH, 250));
    }
  }

  /**
   * 高度な遅延読み込み初期化
   */
  function initAdvancedLazyLoading() {
    // Intersection Observer APIをサポートしているかチェック
    if (!('IntersectionObserver' in window)) {
      console.log(
        '[Lazy Loading] IntersectionObserver not supported, falling back to immediate loading',
      );
      return;
    }

    // 遅延読み込み対象の画像を取得
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');

    if (lazyImages.length === 0) {
      return;
    }

    // Intersection Observerの設定
    const imageObserver = new IntersectionObserver(
      function (entries, observer) {
        entries.forEach(function (entry) {
          if (entry.isIntersecting) {
            const img = entry.target;

            // 画像の読み込み開始
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }

            // 読み込み完了時の処理
            img.addEventListener('load', function () {
              img.classList.add('loaded');
            });

            // 監視を停止
            observer.unobserve(img);
          }
        });
      },
      {
        // ビューポートの50px手前で読み込み開始
        rootMargin: '50px 0px',
        threshold: 0.01,
      },
    );

    // 各画像を監視対象に追加
    lazyImages.forEach(function (img) {
      imageObserver.observe(img);
    });

    console.log(`[Lazy Loading] ${lazyImages.length}個の画像を遅延読み込み対象に設定しました`);
  }

  /**
   * パフォーマンス監視機能
   */
  function initPerformanceMonitoring() {
    // Performance Observer APIをサポートしているかチェック
    if (!('PerformanceObserver' in window)) {
      return;
    }

    // LCP (Largest Contentful Paint) を監視
    const lcpObserver = new PerformanceObserver(function (list) {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log(`[Performance] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
    });

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (_error) {
      // サポートされていない場合は無視
      console.log('[Performance] LCP monitoring not supported');
    }

    // FID (First Input Delay) を監視
    const fidObserver = new PerformanceObserver(function (list) {
      const entries = list.getEntries();
      entries.forEach(function (entry) {
        console.log(`[Performance] FID: ${entry.processingStart - entry.startTime}ms`);
      });
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (_error) {
      // サポートされていない場合は無視
      console.log('[Performance] FID monitoring not supported');
    }
  }

  /**
   * デバッグ用：無限スクロールの状態を確認
   */
  function debugInfiniteScroll() {
    const pcTracks = document.querySelectorAll('.owned-media-partner-logos__pc-scrolling-track');
    const mobileTracks = document.querySelectorAll('.owned-media-partner-logos__scrolling-track');

    console.log('=== 無限スクロール デバッグ情報 ===');

    pcTracks.forEach((track, index) => {
      console.log(`PC Track ${index + 1}:`);
      console.log(`  - 子要素数: ${track.children.length}`);
      console.log(`  - offsetWidth: ${track.offsetWidth}px`);
      console.log(`  - scrollWidth: ${track.scrollWidth}px`);
      console.log(`  - transform: ${track.style.transform}`);
    });

    mobileTracks.forEach((track, index) => {
      console.log(`Mobile Track ${index + 1}:`);
      console.log(`  - 子要素数: ${track.children.length}`);
      console.log(`  - offsetWidth: ${track.offsetWidth}px`);
      console.log(`  - scrollWidth: ${track.scrollWidth}px`);
      console.log(`  - transform: ${track.style.transform}`);
    });

    console.log('=== デバッグ情報終了 ===');
  }

  // パフォーマンス監視を初期化（開発環境のみ）
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    initPerformanceMonitoring();
  }

  // デバッグ関数をグローバルに公開
  window.debugInfiniteScroll = debugInfiniteScroll;

  // タッチ最適化を初期化
  initTouchOptimizations();
})();
